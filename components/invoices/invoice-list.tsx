'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Invoice, InvoiceStatus } from '@/lib/types/database';
import { getInvoices } from '@/lib/services/database';
import { formatCurrency } from '@/lib/utils/calculations';
import { Eye, Edit, Plus } from 'lucide-react';

interface InvoiceListProps {
  onCreateNew?: () => void;
  onViewInvoice?: (invoice: Invoice) => void;
  onEditInvoice?: (invoice: Invoice) => void;
}

const STATUS_COLORS: Record<InvoiceStatus, string> = {
  draft: 'bg-gray-100 text-gray-800',
  sent: 'bg-blue-100 text-blue-800',
  paid: 'bg-green-100 text-green-800',
  overdue: 'bg-red-100 text-red-800',
  cancelled: 'bg-gray-100 text-gray-600',
};

export function InvoiceList({ onCreateNew, onViewInvoice, onEditInvoice }: InvoiceListProps) {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const limit = 20;

  useEffect(() => {
    loadInvoices();
  }, [currentPage]);

  const loadInvoices = async () => {
    setIsLoading(true);
    setError(null);
    
    const result = await getInvoices(currentPage, limit);
    if (result.error) {
      setError(result.error);
    } else if (result.data) {
      setInvoices(result.data.data);
      setTotalCount(result.data.count);
    }
    
    setIsLoading(false);
  };

  const getCustomerName = (invoice: Invoice) => {
    if (!invoice.customer) return 'Unknown Customer';
    
    if (invoice.customer.type === 'company') {
      return invoice.customer.company_name || 'Unnamed Company';
    } else {
      return `${invoice.customer.first_name || ''} ${invoice.customer.last_name || ''}`.trim() || 'Unnamed Individual';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const totalPages = Math.ceil(totalCount / limit);

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-red-500 mb-4">Error loading invoices: {error}</p>
            <Button onClick={loadInvoices} variant="outline">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Invoices</CardTitle>
            {onCreateNew && (
              <Button onClick={onCreateNew}>
                <Plus className="h-4 w-4 mr-2" />
                Create Invoice
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading invoices...</div>
          ) : invoices.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No invoices found</p>
              {onCreateNew && (
                <Button onClick={onCreateNew}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Invoice
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {/* Invoice Table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Invoice #</th>
                      <th className="text-left p-2">Customer</th>
                      <th className="text-left p-2">Date</th>
                      <th className="text-left p-2">Due Date</th>
                      <th className="text-left p-2">Status</th>
                      <th className="text-left p-2">Channel</th>
                      <th className="text-right p-2">Total</th>
                      <th className="text-center p-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoices.map((invoice) => (
                      <tr key={invoice.id} className="border-b hover:bg-muted/50">
                        <td className="p-2 font-medium">#{invoice.invoice_number}</td>
                        <td className="p-2">{getCustomerName(invoice)}</td>
                        <td className="p-2">{formatDate(invoice.invoice_date)}</td>
                        <td className="p-2">
                          {invoice.due_date ? formatDate(invoice.due_date) : '-'}
                        </td>
                        <td className="p-2">
                          <Badge className={STATUS_COLORS[invoice.status]}>
                            {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                          </Badge>
                        </td>
                        <td className="p-2 capitalize">
                          {invoice.sales_channel.replace('_', ' ')}
                        </td>
                        <td className="p-2 text-right font-medium">
                          {formatCurrency(invoice.total_amount)}
                        </td>
                        <td className="p-2">
                          <div className="flex gap-1 justify-center">
                            {onViewInvoice && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onViewInvoice(invoice)}
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            )}
                            {onEditInvoice && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onEditInvoice(invoice)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center pt-4">
                  <p className="text-sm text-muted-foreground">
                    Showing {(currentPage - 1) * limit + 1} to {Math.min(currentPage * limit, totalCount)} of {totalCount} invoices
                  </p>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <span className="flex items-center px-3 text-sm">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
