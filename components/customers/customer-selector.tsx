'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Customer } from '@/lib/types/database';
import { getCustomers } from '@/lib/services/database';
import { Plus } from 'lucide-react';

interface CustomerSelectorProps {
  value?: string;
  onValueChange: (customerId: string) => void;
  onAddNew?: () => void;
}

export function CustomerSelector({ value, onValueChange, onAddNew }: CustomerSelectorProps) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    setIsLoading(true);
    setError(null);
    
    const result = await getCustomers();
    if (result.error) {
      setError(result.error);
    } else {
      setCustomers(result.data || []);
    }
    
    setIsLoading(false);
  };

  const getCustomerDisplayName = (customer: Customer) => {
    if (customer.type === 'company') {
      return customer.company_name || 'Unnamed Company';
    } else {
      return `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || 'Unnamed Individual';
    }
  };

  if (error) {
    return (
      <div className="space-y-2">
        <Label>Customer</Label>
        <div className="text-red-500 text-sm">Error loading customers: {error}</div>
        <Button onClick={loadCustomers} variant="outline" size="sm">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="customer">Customer</Label>
      <div className="flex gap-2">
        <Select value={value} onValueChange={onValueChange} disabled={isLoading}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder={isLoading ? "Loading customers..." : "Select a customer"} />
          </SelectTrigger>
          <SelectContent>
            {customers.map((customer) => (
              <SelectItem key={customer.id} value={customer.id}>
                <div className="flex flex-col">
                  <span>{getCustomerDisplayName(customer)}</span>
                  {customer.email && (
                    <span className="text-xs text-muted-foreground">{customer.email}</span>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {onAddNew && (
          <Button type="button" variant="outline" size="icon" onClick={onAddNew}>
            <Plus className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
