import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { FileText, Users, Package, TrendingUp } from "lucide-react";

export default function Home() {
  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">
          Welcome to Planty Invoice
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Simple and efficient invoice management for your small business.
          Create, track, and manage invoices with ease.
        </p>
        <div className="flex gap-4 justify-center">
          <Link href="/invoices">
            <Button size="lg">
              <FileText className="h-5 w-5 mr-2" />
              Create Invoice
            </Button>
          </Link>
          <Link href="/invoices">
            <Button variant="outline" size="lg">
              View All Invoices
            </Button>
          </Link>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <FileText className="h-12 w-12 mx-auto text-primary" />
            <CardTitle>Invoices</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              Create and manage invoices with automatic calculations
            </p>
            <Link href="/invoices">
              <Button className="w-full">Manage Invoices</Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <Users className="h-12 w-12 mx-auto text-primary" />
            <CardTitle>Customers</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              Add and organize your customer information
            </p>
            <Link href="/invoices">
              <Button className="w-full" variant="outline">
                Manage Customers
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <Package className="h-12 w-12 mx-auto text-primary" />
            <CardTitle>Products</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              Set up your product catalog with variants
            </p>
            <Link href="/invoices">
              <Button className="w-full" variant="outline">
                Manage Products
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Features */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-center">Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex gap-4">
            <TrendingUp className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-semibold">Automatic Calculations</h3>
              <p className="text-muted-foreground">
                VAT, commissions, and totals calculated automatically
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <FileText className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-semibold">Professional Invoices</h3>
              <p className="text-muted-foreground">
                Create professional invoices with all required details
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <Users className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-semibold">Customer Management</h3>
              <p className="text-muted-foreground">
                Support for both individual and company customers
              </p>
            </div>
          </div>
          <div className="flex gap-4">
            <Package className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-semibold">Product Variants</h3>
              <p className="text-muted-foreground">
                Manage products with colors, scents, and other variants
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
