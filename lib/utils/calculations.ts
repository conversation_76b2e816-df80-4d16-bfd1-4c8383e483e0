import { InvoiceItemFormData, CommissionRate, SalesChannel, PaymentMethod } from '@/lib/types/database';

export interface InvoiceCalculations {
  subtotal: number;
  vatAmount: number;
  commissionAmount: number;
  shippingAmount: number;
  totalAmount: number;
}

export function calculateInvoiceTotals(
  items: InvoiceItemFormData[],
  vatPercentage: number = 21,
  shippingAmount: number = 0,
  commissionPercentage: number = 0
): InvoiceCalculations {
  const subtotal = items.reduce((sum, item) => sum + item.line_total, 0);
  const vatAmount = (subtotal * vatPercentage) / 100;
  const commissionAmount = (subtotal * commissionPercentage) / 100;
  const totalAmount = subtotal + vatAmount + shippingAmount;

  return {
    subtotal,
    vatAmount,
    commissionAmount,
    shippingAmount,
    totalAmount
  };
}

export function calculateLineTotal(unitPrice: number, quantity: number): number {
  return unitPrice * quantity;
}

export function findCommissionRate(
  commissionRates: CommissionRate[],
  salesChannel: SalesChannel,
  paymentMethod?: PaymentMethod
): CommissionRate | undefined {
  // First try to find exact match with payment method
  if (paymentMethod) {
    const exactMatch = commissionRates.find(
      rate => rate.sales_channel === salesChannel && 
               rate.payment_method === paymentMethod &&
               rate.active
    );
    if (exactMatch) return exactMatch;
  }

  // Fallback to sales channel only
  return commissionRates.find(
    rate => rate.sales_channel === salesChannel && 
             !rate.payment_method &&
             rate.active
  );
}

export function formatCurrency(amount: number, currency: string = '€'): string {
  return `${currency}${amount.toFixed(2)}`;
}

export function formatPercentage(percentage: number): string {
  return `${percentage.toFixed(2)}%`;
}

// Validation helpers
export function validateInvoiceItems(items: InvoiceItemFormData[]): string[] {
  const errors: string[] = [];

  if (items.length === 0) {
    errors.push('At least one invoice item is required');
  }

  items.forEach((item, index) => {
    if (!item.product_id) {
      errors.push(`Item ${index + 1}: Product is required`);
    }
    if (!item.description.trim()) {
      errors.push(`Item ${index + 1}: Description is required`);
    }
    if (item.quantity <= 0) {
      errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
    }
    if (item.unit_price < 0) {
      errors.push(`Item ${index + 1}: Unit price cannot be negative`);
    }
  });

  return errors;
}

export function validateInvoiceForm(
  customerId: string,
  items: InvoiceItemFormData[],
  invoiceDate: string
): string[] {
  const errors: string[] = [];

  if (!customerId) {
    errors.push('Customer is required');
  }

  if (!invoiceDate) {
    errors.push('Invoice date is required');
  }

  errors.push(...validateInvoiceItems(items));

  return errors;
}
