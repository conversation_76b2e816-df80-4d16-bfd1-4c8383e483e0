// Database types matching the Supabase schema

export type CustomerType = 'individual' | 'company';
export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
export type PaymentMethod = 'cash' | 'card' | 'bank_transfer' | 'shopify';
export type SalesChannel = 'in_store' | 'website' | 'partner_store';

export interface Customer {
  id: string;
  type: CustomerType;
  company_name?: string;
  nif?: string;
  vat_number?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  street_address?: string;
  city?: string;
  zip_code?: string;
  country?: string;
  commission_percentage?: number;
  relationship_started_date?: string;
}

export interface ProductType {
  id: string;
  name: string;
  description?: string;
  weight_grams?: number;
  price: number;
}

export interface Product {
  id: string;
  product_type_id: string;
  sku?: string;
  color?: string;
  scent?: string;
  active: boolean;
  product_type?: ProductType; // For joined queries
}

export interface Invoice {
  id: string;
  invoice_number: number;
  customer_id: string;
  invoice_date: string;
  due_date?: string;
  status: InvoiceStatus;
  sales_channel: SalesChannel;
  payment_method?: PaymentMethod;
  subtotal: number;
  commission_amount: number;
  commission_percentage?: number;
  shipping_amount: number;
  vat_percentage: number;
  vat_amount: number;
  total_amount: number;
  payment_reference?: string;
  paid_date?: string;
  notes?: string;
  customer?: Customer; // For joined queries
  invoice_items?: InvoiceItem[]; // For joined queries
}

export interface InvoiceItem {
  id: string;
  invoice_id: string;
  product_id: string;
  description: string;
  unit_price: number;
  quantity: number;
  line_total: number;
  product?: Product; // For joined queries
}

export interface CommissionRate {
  id: string;
  sales_channel: SalesChannel;
  payment_method?: PaymentMethod;
  rate_percentage: number;
  description?: string;
  active: boolean;
}

// Form types for creating/updating records
export interface CreateCustomerData {
  type: CustomerType;
  company_name?: string;
  nif?: string;
  vat_number?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  street_address?: string;
  city?: string;
  zip_code?: string;
  country?: string;
  commission_percentage?: number;
}

export interface CreateInvoiceData {
  customer_id: string;
  invoice_date: string;
  due_date?: string;
  sales_channel: SalesChannel;
  payment_method?: PaymentMethod;
  shipping_amount?: number;
  vat_percentage?: number;
  notes?: string;
  items: CreateInvoiceItemData[];
}

export interface CreateInvoiceItemData {
  product_id: string;
  description: string;
  unit_price: number;
  quantity: number;
}

export interface CreateProductTypeData {
  name: string;
  description?: string;
  weight_grams?: number;
  price: number;
}

export interface CreateProductData {
  product_type_id: string;
  sku?: string;
  color?: string;
  scent?: string;
}

// Utility types for form handling
export interface InvoiceFormData {
  customer_id: string;
  invoice_date: string;
  due_date?: string;
  sales_channel: SalesChannel;
  payment_method?: PaymentMethod;
  shipping_amount: number;
  vat_percentage: number;
  notes?: string;
  items: InvoiceItemFormData[];
}

export interface InvoiceItemFormData {
  product_id: string;
  description: string;
  unit_price: number;
  quantity: number;
  line_total: number;
}

// API response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
}
