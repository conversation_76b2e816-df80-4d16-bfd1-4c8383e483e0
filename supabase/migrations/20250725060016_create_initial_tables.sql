create type "public"."customer_type" as enum ('individual', 'company');

create type "public"."invoice_status" as enum ('draft', 'sent', 'paid', 'overdue', 'cancelled');

create type "public"."payment_method" as enum ('cash', 'card', 'bank_transfer', 'shopify');

create type "public"."sales_channel" as enum ('in_store', 'website', 'partner_store');

create table "public"."commission_rates" (
    "id" uuid not null default uuid_generate_v4(),
    "sales_channel" sales_channel not null,
    "payment_method" payment_method,
    "rate_percentage" numeric(5,2) not null,
    "description" text,
    "active" boolean default true
);


create table "public"."customers" (
    "id" uuid not null default uuid_generate_v4(),
    "type" customer_type not null,
    "company_name" text,
    "nif" text,
    "vat_number" text,
    "first_name" text,
    "last_name" text,
    "email" text,
    "phone" text,
    "street_address" text,
    "city" text,
    "zip_code" text,
    "country" text default 'Spain'::text,
    "commission_percentage" numeric(5,2),
    "relationship_started_date" date default CURRENT_DATE
);


create table "public"."invoice_items" (
    "id" uuid not null default uuid_generate_v4(),
    "invoice_id" uuid not null,
    "product_id" uuid not null,
    "description" text not null,
    "unit_price" numeric(10,2) not null,
    "quantity" integer not null,
    "line_total" numeric(10,2) not null
);


create table "public"."invoices" (
    "id" uuid not null default uuid_generate_v4(),
    "invoice_number" integer not null,
    "customer_id" uuid not null,
    "invoice_date" date not null,
    "due_date" date,
    "status" invoice_status default 'draft'::invoice_status,
    "sales_channel" sales_channel not null,
    "payment_method" payment_method,
    "subtotal" numeric(10,2) not null,
    "commission_amount" numeric(10,2) default 0,
    "commission_percentage" numeric(5,2),
    "shipping_amount" numeric(10,2) default 0,
    "vat_percentage" numeric(5,2) default 21.00,
    "vat_amount" numeric(10,2) not null,
    "total_amount" numeric(10,2) not null,
    "payment_reference" text,
    "paid_date" date,
    "notes" text
);


create table "public"."product_types" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null,
    "description" text,
    "weight_grams" integer,
    "price" numeric(10,2) not null
);


create table "public"."products" (
    "id" uuid not null default uuid_generate_v4(),
    "product_type_id" uuid not null,
    "sku" text,
    "color" text,
    "scent" text,
    "active" boolean default true
);


CREATE UNIQUE INDEX commission_rates_pkey ON public.commission_rates USING btree (id);

CREATE UNIQUE INDEX commission_rates_sales_channel_payment_method_key ON public.commission_rates USING btree (sales_channel, payment_method);

CREATE UNIQUE INDEX customers_pkey ON public.customers USING btree (id);

CREATE INDEX idx_customers_type ON public.customers USING btree (type);

CREATE INDEX idx_invoice_items_invoice_id ON public.invoice_items USING btree (invoice_id);

CREATE INDEX idx_invoice_items_product_id ON public.invoice_items USING btree (product_id);

CREATE INDEX idx_invoices_customer_id ON public.invoices USING btree (customer_id);

CREATE INDEX idx_invoices_invoice_date ON public.invoices USING btree (invoice_date);

CREATE INDEX idx_invoices_sales_channel ON public.invoices USING btree (sales_channel);

CREATE INDEX idx_invoices_status ON public.invoices USING btree (status);

CREATE INDEX idx_products_active ON public.products USING btree (active);

CREATE INDEX idx_products_color ON public.products USING btree (color);

CREATE INDEX idx_products_product_type_id ON public.products USING btree (product_type_id);

CREATE INDEX idx_products_scent ON public.products USING btree (scent);

CREATE UNIQUE INDEX invoice_items_pkey ON public.invoice_items USING btree (id);

CREATE UNIQUE INDEX invoices_invoice_number_key ON public.invoices USING btree (invoice_number);

CREATE UNIQUE INDEX invoices_pkey ON public.invoices USING btree (id);

CREATE UNIQUE INDEX product_types_name_key ON public.product_types USING btree (name);

CREATE UNIQUE INDEX product_types_pkey ON public.product_types USING btree (id);

CREATE UNIQUE INDEX products_pkey ON public.products USING btree (id);

CREATE UNIQUE INDEX products_product_type_id_color_scent_key ON public.products USING btree (product_type_id, color, scent);

CREATE UNIQUE INDEX products_sku_key ON public.products USING btree (sku);

alter table "public"."commission_rates" add constraint "commission_rates_pkey" PRIMARY KEY using index "commission_rates_pkey";

alter table "public"."customers" add constraint "customers_pkey" PRIMARY KEY using index "customers_pkey";

alter table "public"."invoice_items" add constraint "invoice_items_pkey" PRIMARY KEY using index "invoice_items_pkey";

alter table "public"."invoices" add constraint "invoices_pkey" PRIMARY KEY using index "invoices_pkey";

alter table "public"."product_types" add constraint "product_types_pkey" PRIMARY KEY using index "product_types_pkey";

alter table "public"."products" add constraint "products_pkey" PRIMARY KEY using index "products_pkey";

alter table "public"."commission_rates" add constraint "commission_rates_sales_channel_payment_method_key" UNIQUE using index "commission_rates_sales_channel_payment_method_key";

alter table "public"."customers" add constraint "check_company_required_fields" CHECK (((type <> 'company'::customer_type) OR ((company_name IS NOT NULL) AND (nif IS NOT NULL)))) not valid;

alter table "public"."customers" validate constraint "check_company_required_fields";

alter table "public"."customers" add constraint "check_individual_required_fields" CHECK (((type <> 'individual'::customer_type) OR ((first_name IS NOT NULL) AND (last_name IS NOT NULL)))) not valid;

alter table "public"."customers" validate constraint "check_individual_required_fields";

alter table "public"."invoice_items" add constraint "invoice_items_invoice_id_fkey" FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE not valid;

alter table "public"."invoice_items" validate constraint "invoice_items_invoice_id_fkey";

alter table "public"."invoice_items" add constraint "invoice_items_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) not valid;

alter table "public"."invoice_items" validate constraint "invoice_items_product_id_fkey";

alter table "public"."invoices" add constraint "invoices_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."invoices" validate constraint "invoices_customer_id_fkey";

alter table "public"."invoices" add constraint "invoices_invoice_number_key" UNIQUE using index "invoices_invoice_number_key";

alter table "public"."product_types" add constraint "product_types_name_key" UNIQUE using index "product_types_name_key";

alter table "public"."products" add constraint "products_product_type_id_color_scent_key" UNIQUE using index "products_product_type_id_color_scent_key";

alter table "public"."products" add constraint "products_product_type_id_fkey" FOREIGN KEY (product_type_id) REFERENCES product_types(id) not valid;

alter table "public"."products" validate constraint "products_product_type_id_fkey";

alter table "public"."products" add constraint "products_sku_key" UNIQUE using index "products_sku_key";

grant delete on table "public"."commission_rates" to "anon";

grant insert on table "public"."commission_rates" to "anon";

grant references on table "public"."commission_rates" to "anon";

grant select on table "public"."commission_rates" to "anon";

grant trigger on table "public"."commission_rates" to "anon";

grant truncate on table "public"."commission_rates" to "anon";

grant update on table "public"."commission_rates" to "anon";

grant delete on table "public"."commission_rates" to "authenticated";

grant insert on table "public"."commission_rates" to "authenticated";

grant references on table "public"."commission_rates" to "authenticated";

grant select on table "public"."commission_rates" to "authenticated";

grant trigger on table "public"."commission_rates" to "authenticated";

grant truncate on table "public"."commission_rates" to "authenticated";

grant update on table "public"."commission_rates" to "authenticated";

grant delete on table "public"."commission_rates" to "service_role";

grant insert on table "public"."commission_rates" to "service_role";

grant references on table "public"."commission_rates" to "service_role";

grant select on table "public"."commission_rates" to "service_role";

grant trigger on table "public"."commission_rates" to "service_role";

grant truncate on table "public"."commission_rates" to "service_role";

grant update on table "public"."commission_rates" to "service_role";

grant delete on table "public"."customers" to "anon";

grant insert on table "public"."customers" to "anon";

grant references on table "public"."customers" to "anon";

grant select on table "public"."customers" to "anon";

grant trigger on table "public"."customers" to "anon";

grant truncate on table "public"."customers" to "anon";

grant update on table "public"."customers" to "anon";

grant delete on table "public"."customers" to "authenticated";

grant insert on table "public"."customers" to "authenticated";

grant references on table "public"."customers" to "authenticated";

grant select on table "public"."customers" to "authenticated";

grant trigger on table "public"."customers" to "authenticated";

grant truncate on table "public"."customers" to "authenticated";

grant update on table "public"."customers" to "authenticated";

grant delete on table "public"."customers" to "service_role";

grant insert on table "public"."customers" to "service_role";

grant references on table "public"."customers" to "service_role";

grant select on table "public"."customers" to "service_role";

grant trigger on table "public"."customers" to "service_role";

grant truncate on table "public"."customers" to "service_role";

grant update on table "public"."customers" to "service_role";

grant delete on table "public"."invoice_items" to "anon";

grant insert on table "public"."invoice_items" to "anon";

grant references on table "public"."invoice_items" to "anon";

grant select on table "public"."invoice_items" to "anon";

grant trigger on table "public"."invoice_items" to "anon";

grant truncate on table "public"."invoice_items" to "anon";

grant update on table "public"."invoice_items" to "anon";

grant delete on table "public"."invoice_items" to "authenticated";

grant insert on table "public"."invoice_items" to "authenticated";

grant references on table "public"."invoice_items" to "authenticated";

grant select on table "public"."invoice_items" to "authenticated";

grant trigger on table "public"."invoice_items" to "authenticated";

grant truncate on table "public"."invoice_items" to "authenticated";

grant update on table "public"."invoice_items" to "authenticated";

grant delete on table "public"."invoice_items" to "service_role";

grant insert on table "public"."invoice_items" to "service_role";

grant references on table "public"."invoice_items" to "service_role";

grant select on table "public"."invoice_items" to "service_role";

grant trigger on table "public"."invoice_items" to "service_role";

grant truncate on table "public"."invoice_items" to "service_role";

grant update on table "public"."invoice_items" to "service_role";

grant delete on table "public"."invoices" to "anon";

grant insert on table "public"."invoices" to "anon";

grant references on table "public"."invoices" to "anon";

grant select on table "public"."invoices" to "anon";

grant trigger on table "public"."invoices" to "anon";

grant truncate on table "public"."invoices" to "anon";

grant update on table "public"."invoices" to "anon";

grant delete on table "public"."invoices" to "authenticated";

grant insert on table "public"."invoices" to "authenticated";

grant references on table "public"."invoices" to "authenticated";

grant select on table "public"."invoices" to "authenticated";

grant trigger on table "public"."invoices" to "authenticated";

grant truncate on table "public"."invoices" to "authenticated";

grant update on table "public"."invoices" to "authenticated";

grant delete on table "public"."invoices" to "service_role";

grant insert on table "public"."invoices" to "service_role";

grant references on table "public"."invoices" to "service_role";

grant select on table "public"."invoices" to "service_role";

grant trigger on table "public"."invoices" to "service_role";

grant truncate on table "public"."invoices" to "service_role";

grant update on table "public"."invoices" to "service_role";

grant delete on table "public"."product_types" to "anon";

grant insert on table "public"."product_types" to "anon";

grant references on table "public"."product_types" to "anon";

grant select on table "public"."product_types" to "anon";

grant trigger on table "public"."product_types" to "anon";

grant truncate on table "public"."product_types" to "anon";

grant update on table "public"."product_types" to "anon";

grant delete on table "public"."product_types" to "authenticated";

grant insert on table "public"."product_types" to "authenticated";

grant references on table "public"."product_types" to "authenticated";

grant select on table "public"."product_types" to "authenticated";

grant trigger on table "public"."product_types" to "authenticated";

grant truncate on table "public"."product_types" to "authenticated";

grant update on table "public"."product_types" to "authenticated";

grant delete on table "public"."product_types" to "service_role";

grant insert on table "public"."product_types" to "service_role";

grant references on table "public"."product_types" to "service_role";

grant select on table "public"."product_types" to "service_role";

grant trigger on table "public"."product_types" to "service_role";

grant truncate on table "public"."product_types" to "service_role";

grant update on table "public"."product_types" to "service_role";

grant delete on table "public"."products" to "anon";

grant insert on table "public"."products" to "anon";

grant references on table "public"."products" to "anon";

grant select on table "public"."products" to "anon";

grant trigger on table "public"."products" to "anon";

grant truncate on table "public"."products" to "anon";

grant update on table "public"."products" to "anon";

grant delete on table "public"."products" to "authenticated";

grant insert on table "public"."products" to "authenticated";

grant references on table "public"."products" to "authenticated";

grant select on table "public"."products" to "authenticated";

grant trigger on table "public"."products" to "authenticated";

grant truncate on table "public"."products" to "authenticated";

grant update on table "public"."products" to "authenticated";

grant delete on table "public"."products" to "service_role";

grant insert on table "public"."products" to "service_role";

grant references on table "public"."products" to "service_role";

grant select on table "public"."products" to "service_role";

grant trigger on table "public"."products" to "service_role";

grant truncate on table "public"."products" to "service_role";

grant update on table "public"."products" to "service_role";


