-- Customers table - both individuals and companies
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type customer_type NOT NULL,
    
    -- Company fields
    company_name TEXT, -- Required for companies
    nif TEXT, -- Spanish tax ID for companies
    vat_number TEXT, -- EU VAT number if applicable
    
    -- Individual fields  
    first_name TEXT, -- Required for individuals
    last_name TEXT, -- Required for individuals
    
    -- Contact information (common)
    email TEXT,
    phone TEXT,
    
    -- Address (common)
    street_address TEXT,
    city TEXT,
    zip_code TEXT,
    country TEXT DEFAULT 'Spain',
    
    -- Business relationship fields
    commission_percentage DECIMAL(5,2), -- For companies that take a percentage
    
    -- Track when relationship started (useful for business analytics)
    relationship_started_date DATE DEFAULT CURRENT_DATE,
    
    -- Constraints to ensure required fields based on type
    CONSTRAINT check_company_required_fields 
        CHECK (type != 'company' OR (company_name IS NOT NULL AND nif IS NOT NULL)),
    CONSTRAINT check_individual_required_fields 
        CHECK (type != 'individual' OR (first_name IS NOT NULL AND last_name IS NOT NULL))
);

-- Indexes for customers
CREATE INDEX idx_customers_type ON customers(type);