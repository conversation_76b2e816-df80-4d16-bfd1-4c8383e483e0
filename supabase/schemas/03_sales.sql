-- Invoices table
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_number INTEGER NOT NULL UNIQUE, -- Sequential integer invoice numbers
    customer_id UUID NOT NULL REFERENCES customers(id),
    
    -- Invoice details
    invoice_date DATE NOT NULL,
    due_date DATE, -- For companies with payment terms
    status invoice_status DEFAULT 'draft',
    
    -- Sales context
    sales_channel sales_channel NOT NULL,
    payment_method payment_method,
    
    -- Financial totals (calculated from line items but stored for performance)
    subtotal DECIMAL(10,2) NOT NULL, -- Before commissions and VAT
    commission_amount DECIMAL(10,2) DEFAULT 0, -- Commission deducted
    commission_percentage DECIMAL(5,2), -- Commission rate applied
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    vat_percentage DECIMAL(5,2) DEFAULT 21.00, -- Spanish VAT rate
    vat_amount DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL, -- Final amount
    
    -- Payment information
    payment_reference TEXT, -- Bank reference, transaction ID, etc.
    paid_date DATE,
    
    -- Additional notes
    notes TEXT
);

-- Invoice line items
CREATE TABLE invoice_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    
    -- Item details
    description TEXT NOT NULL, -- Product name at time of sale
    unit_price DECIMAL(10,2) NOT NULL, -- Price per unit at time of sale
    quantity INTEGER NOT NULL,
    line_total DECIMAL(10,2) NOT NULL -- unit_price * quantity
);

-- Indexes for sales tables
CREATE INDEX idx_invoices_customer_id ON invoices(customer_id);
CREATE INDEX idx_invoices_invoice_date ON invoices(invoice_date);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_sales_channel ON invoices(sales_channel);
CREATE INDEX idx_invoice_items_invoice_id ON invoice_items(invoice_id);
CREATE INDEX idx_invoice_items_product_id ON invoice_items(product_id);