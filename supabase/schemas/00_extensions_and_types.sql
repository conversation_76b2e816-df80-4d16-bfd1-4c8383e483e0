-- Enable UUID extension (Supabase usually has this enabled by default)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Customer types: 'individual' for end customers, 'company' for business partners
CREATE TYPE customer_type AS ENUM ('individual', 'company');

-- Sales channels
CREATE TYPE sales_channel AS ENUM ('in_store', 'website', 'partner_store');

-- Payment methods
CREATE TYPE payment_method AS ENUM ('cash', 'card', 'bank_transfer', 'shopify');

-- Invoice statuses
CREATE TYPE invoice_status AS ENUM ('draft', 'sent', 'paid', 'overdue', 'cancelled');