-- Product categories/types (e.g., "Madrid candle", "Barcelona candle")
CREATE TABLE product_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    weight_grams INTEGER,
    price DECIMAL(10,2) NOT NULL -- Price is set at the candle design level
);

-- Product variants - specific combinations of type, color, and scent
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_type_id UUID NOT NULL REFERENCES product_types(id),
    sku TEXT UNIQUE, -- Optional SKU for easier identification
    color TEXT,
    scent TEXT,
    
    -- Future inventory fields (for scalability)
    -- stock_quantity INTEGER DEFAULT 0,
    -- min_stock_level INTEGER DEFAULT 0,
    -- cost_price DECIMAL(10,2), -- Your cost to make this variant
    
    active BOOLEAN DEFAULT true, -- To disable variants without deleting
    
    -- Ensure unique combinations of type, color, and scent
    UNIQUE(product_type_id, color, scent)
);

-- Indexes for products
CREATE INDEX idx_products_product_type_id ON products(product_type_id);
CREATE INDEX idx_products_color ON products(color);
CREATE INDEX idx_products_scent ON products(scent);
CREATE INDEX idx_products_active ON products(active);